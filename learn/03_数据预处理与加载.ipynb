try:
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(
        data, image_dir, tokenizer, batch_size=2, num_workers=0  # 使用小批次和0个worker便于调试
    )
    
    print(f"数据加载器创建成功:")
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    
    # 获取一个训练批次
    print("\n获取一个训练批次...")
    for batch_idx, (image_ids, images, report_ids, report_masks) in enumerate(train_loader):
        print(f"\n批次 {batch_idx + 1}:")
        print(f"图像ID: {image_ids}")
        print(f"图像张量形状: {images.shape}")
        print(f"报告ID张量形状: {report_ids.shape}")
        print(f"报告掩码张量形状: {report_masks.shape}")
        
        # 显示第一个样本的详细信息
        print(f"\n第一个样本详情:")
        print(f"图像像素值范围: [{images[0].min():.3f}, {images[0].max():.3f}]")
        print(f"报告ID (前20个): {report_ids[0][:20].tolist()}")
        print(f"报告掩码 (前20个): {report_masks[0][:20].tolist()}")
        
        # 解码报告
        decoded_report = tokenizer.decode(report_ids[0].tolist())
        print(f"解码后的报告: {decoded_report}")
        
        break  # 只查看第一个批次
    
    # 可视化批次中的图像
    def visualize_batch(images, image_ids, max_samples=2):
        """
        可视化批次中的图像
        
        Args:
            images (torch.Tensor): 图像张量 (batch_size, 2, 3, 224, 224)
            image_ids (list): 图像ID列表
            max_samples (int): 最大显示样本数
        """
        batch_size = min(images.shape[0], max_samples)
        
        fig, axes = plt.subplots(batch_size, 2, figsize=(10, 5 * batch_size))
        if batch_size == 1:
            axes = axes.reshape(1, -1)
        
        # 反标准化参数
        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
        
        for i in range(batch_size):
            for j in range(2):  # 两张图像
                # 反标准化
                img = images[i, j] * std[0] + mean[0]
                img = torch.clamp(img, 0, 1)
                
                # 转换为numpy并调整维度
                img_np = img.permute(1, 2, 0).numpy()
                
                axes[i, j].imshow(img_np)
                axes[i, j].set_title(f"样本 {image_ids[i]} - 图像 {j+1}")
                axes[i, j].axis('off')
        
        plt.tight_layout()
        plt.show()
    
    print("\n可视化批次图像:")
    visualize_batch(images, image_ids)
    
except Exception as e:
    print(f"创建数据加载器时出错: {e}")
    print("这可能是因为图像文件不存在或路径不正确")


# 图像张量维度 torch.Size([2, 2, 3, 224, 224])
# [批大小, 每样本图像数, 颜色通道, 高度, 宽度]
# [  2  ,      2  ,    3   ,   224, 224]
# 批次包含2个样本，每个样本有2张X光片（正面+侧面视角）

# 报告ID张量形状: torch.Size([2, 60])
# [批大小, 最大序列长度]
# [  2  ,    60     ]
# 第1维(2): 批大小，包含2个报告
# 第2维(60): 每个报告的最大词汇数量（固定长度）

# 报告ID数字序列详解
# [2, 687, 317, 367, 454, 344, 627, 687, 425, 367, 723, 687, 409, 71, 152, 4, 3, 0, 0, 0]
# 简单分词器，对文字分词后对应的数字，每个数字是词汇在词汇表中的索引
# 2: <start>标记（序列开始）
# 687, 317, 367...: 真实词汇的索引号（如 "the", "heart", "is" 等）
# 3: <end>标记（序列结束）
# 0: <pad>标记（填充到固定长度）

# 5. 掩码序列详解
# [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0]

# 掩码机制：让模型知道哪些是真实内容，哪些是填充
# 掩码用于区分真实内容和填充内容：
# 1: 表示对应位置是真实词汇（包括<start>和<end>）
# 0: 表示对应位置是填充的<pad>标记，